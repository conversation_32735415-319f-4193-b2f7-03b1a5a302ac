#!/usr/bin/env python3
"""
等待数据库就绪的脚本
"""
import time
import sys
import os
from sqlalchemy import create_engine, text
from sqlalchemy.exc import OperationalError

def wait_for_database(database_url: str, max_retries: int = 30, retry_interval: int = 2):
    """等待数据库连接可用"""
    print(f"等待数据库连接: {database_url}")
    
    for attempt in range(max_retries):
        try:
            # 创建数据库引擎
            engine = create_engine(database_url)
            
            # 尝试连接数据库
            with engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            
            print("数据库连接成功!")
            return True
            
        except OperationalError as e:
            if attempt < max_retries - 1:
                print(f"数据库连接失败，第 {attempt + 1} 次重试: {str(e)}")
                time.sleep(retry_interval)
            else:
                print(f"数据库连接失败，已达到最大重试次数: {str(e)}")
                return False
        except Exception as e:
            print(f"意外错误: {str(e)}")
            return False
    
    return False

if __name__ == "__main__":
    # 从环境变量获取数据库URL
    database_url = os.getenv("DATABASE_URL", "mysql+pymysql://root:root@db:3306/dormitory_management")
    
    if wait_for_database(database_url):
        print("数据库就绪，启动应用...")
        sys.exit(0)
    else:
        print("数据库连接失败，退出...")
        sys.exit(1)
