# 使用官方 Python 3.12 镜像作为基础镜像
FROM python:3.12-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    pkg-config \
    default-libmysqlclient-dev \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装 Python 依赖
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt


COPY . .

# 创建日志目录
RUN mkdir -p logs

# 复制等待数据库脚本并设置执行权限
COPY wait-for-db.py .
RUN chmod +x wait-for-db.py

# Place executables in the environment at the front of the path
ENV PATH="/app/.venv/bin:$PATH"

# 创建启动脚本
RUN echo '#!/bin/bash\n\
python wait-for-db.py\n\
if [ $? -eq 0 ]; then\n\
    echo "启动应用..."\n\
    python run.py\n\
else\n\
    echo "数据库连接失败，退出..."\n\
    exit 1\n\
fi' > start.sh && chmod +x start.sh

# Run the application.
CMD ["./start.sh"]
