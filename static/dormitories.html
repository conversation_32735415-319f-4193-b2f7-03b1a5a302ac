<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>宿舍管理 - 宿舍入住管理系统</title>
  <link rel="stylesheet" href="css/common.css">
  <link rel="stylesheet" href="css/layout.css">
  <link rel="stylesheet" href="css/components.css">
</head>
<body>
  <div class="app-layout">
    <!-- 侧边栏 -->
    <aside class="app-sidebar">
      <div class="sidebar-header">
        <div class="logo">
          <h2>宿舍管理系统</h2>
        </div>
        <div class="logo-mini hidden">🏠</div>
      </div>
      
      <nav class="menu">
        <div class="menu-item">
          <a href="reports.html">
            <span class="menu-icon">📊</span>
            <span class="menu-title">报表统计</span>
          </a>
        </div>
        <div class="menu-item">
          <a href="records.html">
            <span class="menu-icon">📋</span>
            <span class="menu-title">入住记录</span>
          </a>
        </div>
        <div class="menu-item">
          <a href="departments.html">
            <span class="menu-icon">🏢</span>
            <span class="menu-title">部门管理</span>
          </a>
        </div>
        <div class="menu-item active">
          <a href="dormitories.html">
            <span class="menu-icon">🏠</span>
            <span class="menu-title">宿舍管理</span>
          </a>
        </div>
        <div class="menu-item">
          <a href="residents.html">
            <span class="menu-icon">👤</span>
            <span class="menu-title">住户管理</span>
          </a>
        </div>
      </nav>
    </aside>

    <!-- 主内容区域 -->
    <div class="app-main-container">
      <!-- 顶部导航 -->
      <header class="app-header">
        <div class="header-left">
          <button class="sidebar-toggle">📁</button>
          <nav class="breadcrumb">
            <div class="breadcrumb-item">
              <a href="reports.html">首页</a>
            </div>
            <div class="breadcrumb-item">
              <a href="dormitories.html">宿舍管理</a>
            </div>
          </nav>
        </div>
        
        <div class="header-right">
          <button class="btn refresh-btn" title="刷新页面">🔄</button>
          <div class="user-info">
            <span>👤</span>
            <span class="username">用户</span>
            <span class="arrow-down">▼</span>
            <div class="dropdown-menu">
              <a href="#" class="dropdown-item" onclick="handleUserCommand('userInfo')">
                <span>👤</span>
                个人信息
              </a>
              <a href="#" class="dropdown-item divided" onclick="handleUserCommand('logout')">
                <span>🚪</span>
                退出登录
              </a>
            </div>
          </div>
        </div>
      </header>

      <!-- 主内容 -->
      <main class="app-main">
        <!-- 页面标题 -->
        <div class="page-header">
          <h1 class="page-title">宿舍管理</h1>
          <div class="page-actions">
            <button class="btn btn-primary" onclick="showCreateDialog()">
              <span>➕</span>
              新增宿舍
            </button>
            <button class="btn" onclick="refreshData()">
              <span>🔄</span>
              刷新
            </button>
          </div>
        </div>

        <!-- 搜索筛选 -->
        <div class="card">
          <div class="card__body">
            <form class="search-form" id="searchForm" style="display: flex; gap: 16px; align-items: end;">
              <div class="form-item">
                <label>宿舍名称</label>
                <input type="text" name="name" class="input__inner" placeholder="请输入宿舍名称">
              </div>
              <div class="form-item">
                <button type="button" class="btn btn-primary" onclick="handleSearch()">
                  <span>🔍</span>
                  搜索
                </button>
                <button type="button" class="btn" onclick="resetSearch()">
                  <span>🔄</span>
                  重置
                </button>
              </div>
            </form>
          </div>
        </div>

        <!-- 数据表格 -->
        <div class="card">
          <div class="card__body">
            <div class="table-container">
              <table class="table" id="dormitoriesTable">
                <thead>
                  <tr>
                    <th>宿舍名称</th>
                    <th>总床位数</th>
                    <th>可用床位</th>
                    <th>入住率</th>
                    <th>所属部门</th>
                    <th>描述</th>
                    <th>创建时间</th>
                    <th style="width: 200px;">操作</th>
                  </tr>
                </thead>
                <tbody id="dormitoriesTableBody">
                  <tr>
                    <td colspan="8" style="text-align: center; padding: 40px;">
                      <div class="loading">加载中...</div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <!-- 移动端遮罩 -->
  <div class="sidebar-overlay"></div>

  <!-- 新增/编辑对话框 -->
  <div class="dialog-overlay" id="dormitoryDialog" style="display: none;">
    <div class="dialog">
      <div class="dialog-header">
        <h3 id="dialogTitle">新增宿舍</h3>
      </div>
      <div class="dialog-body">
        <form class="form" id="dormitoryForm">
          <div class="form-item">
            <label class="form-item__label">宿舍名称 *</label>
            <div class="form-item__content">
              <input type="text" name="name" class="input__inner" placeholder="请输入宿舍名称" maxlength="100" required>
            </div>
          </div>
          <div class="form-item">
            <label class="form-item__label">总床位数 *</label>
            <div class="form-item__content">
              <input type="number" name="total_beds" class="input__inner" placeholder="请输入总床位数" min="1" max="20" required>
            </div>
          </div>
          <div class="form-item">
            <label class="form-item__label">所属部门</label>
            <div class="form-item__content">
              <select name="department_id" class="input__inner">
                <option value="">请选择部门</option>
              </select>
            </div>
          </div>
          <div class="form-item">
            <label class="form-item__label">描述</label>
            <div class="form-item__content">
              <textarea name="description" class="input__inner" placeholder="请输入宿舍描述" maxlength="500" style="min-height: 80px; resize: vertical;"></textarea>
            </div>
          </div>
        </form>
      </div>
      <div class="dialog-footer">
        <button type="button" class="btn" onclick="closeDormitoryDialog()">取消</button>
        <button type="button" class="btn btn-primary" onclick="handleSubmit()" id="submitBtn">创建</button>
      </div>
    </div>
  </div>

  <script src="js/utils.js"></script>
  <script src="js/auth.js"></script>
  <script src="js/layout.js"></script>
  <script src="js/page-transition.js"></script>
  <script>
    // 宿舍管理逻辑
    class DormitoriesManager {
      constructor() {
        this.dormitories = [];
        this.departments = [];
        this.loading = false;
        this.currentDormitory = null;
        this.isEdit = false;
        this.searchForm = { name: '' };
        this.init();
      }

      async init() {
        await this.loadDepartments();
        await this.loadDormitories();
      }

      async loadDepartments() {
        try {
          const response = await api.get('/v1/departments?is_active=true');
          this.departments = response.items || response;
          this.renderDepartmentOptions();
        } catch (error) {
          console.error('加载部门失败:', error);
        }
      }

      renderDepartmentOptions() {
        const select = document.querySelector('select[name="department_id"]');
        if (select) {
          select.innerHTML = '<option value="">请选择部门</option>';
          this.departments.forEach(dept => {
            const option = document.createElement('option');
            option.value = dept.id;
            option.textContent = dept.name;
            select.appendChild(option);
          });
        }
      }

      async loadDormitories() {
        this.loading = true;
        this.renderLoading();

        try {
          const params = new URLSearchParams();
          if (this.searchForm.name) {
            params.append('name', this.searchForm.name);
          }

          const response = await api.get(`/v1/dormitories/list?${params.toString()}`);
          this.dormitories = response.items || response;
          this.renderDormitories();
        } catch (error) {
          console.error('加载宿舍失败:', error);
          this.renderError();
        } finally {
          this.loading = false;
        }
      }

      renderLoading() {
        const tbody = document.getElementById('dormitoriesTableBody');
        tbody.innerHTML = `
          <tr>
            <td colspan="8" style="text-align: center; padding: 40px;">
              <div class="loading"></div>
              <div style="margin-top: 12px;">加载中...</div>
            </td>
          </tr>
        `;
      }

      renderDormitories() {
        const tbody = document.getElementById('dormitoriesTableBody');
        
        if (!this.dormitories || this.dormitories.length === 0) {
          tbody.innerHTML = `
            <tr>
              <td colspan="8" style="text-align: center; padding: 40px;">
                <div class="empty">
                  <div class="empty-description">暂无宿舍数据</div>
                </div>
              </td>
            </tr>
          `;
          return;
        }

        tbody.innerHTML = this.dormitories.map(dorm => {
          const occupancyRate = dorm.total_beds > 0 ? 
            Math.round(((dorm.total_beds - (dorm.available_beds || 0)) / dorm.total_beds) * 100) : 0;
          
          return `
            <tr>
              <td>${dorm.name}</td>
              <td>${dorm.total_beds}</td>
              <td>${dorm.available_beds || 0}</td>
              <td>
                <div style="display: flex; align-items: center; gap: 8px;">
                  <div class="progress-bar" style="width: 60px; height: 6px; background: #f0f0f0; border-radius: 3px; overflow: hidden;">
                    <div class="progress-bar-inner" style="width: ${occupancyRate}%; height: 100%; background: ${occupancyRate >= 80 ? '#f56c6c' : occupancyRate >= 60 ? '#e6a23c' : '#67c23a'}; transition: width 0.3s;"></div>
                  </div>
                  <span style="font-size: 12px; color: #666;">${occupancyRate}%</span>
                </div>
              </td>
              <td>${dorm.department_name || '-'}</td>
              <td>${dorm.description || '-'}</td>
              <td>${formatDate(dorm.created_at, 'YYYY-MM-DD HH:mm')}</td>
              <td>
                <div style="display: flex; gap: 8px;">
                  <button class="btn btn-primary" onclick="showEditDialog('${dorm.id}')" style="padding: 4px 8px; font-size: 12px;">编辑</button>
                  <button class="btn btn-danger" onclick="handleDelete('${dorm.id}')" style="padding: 4px 8px; font-size: 12px;">删除</button>
                </div>
              </td>
            </tr>
          `;
        }).join('');
      }

      renderError() {
        const tbody = document.getElementById('dormitoriesTableBody');
        tbody.innerHTML = `
          <tr>
            <td colspan="8" style="text-align: center; padding: 40px;">
              <div class="empty">
                <div class="empty-description">加载失败，请重试</div>
              </div>
            </td>
          </tr>
        `;
      }

      showCreateDialog() {
        this.isEdit = false;
        this.currentDormitory = null;
        document.getElementById('dialogTitle').textContent = '新增宿舍';
        document.getElementById('submitBtn').textContent = '创建';
        document.getElementById('dormitoryForm').reset();
        this.renderDepartmentOptions();
        document.getElementById('dormitoryDialog').style.display = 'flex';
      }

      showEditDialog(dormitoryId) {
        const dormitory = this.dormitories.find(d => d.id === dormitoryId);
        if (!dormitory) {
          showMessage('宿舍不存在', 'error');
          return;
        }

        this.isEdit = true;
        this.currentDormitory = dormitory;
        document.getElementById('dialogTitle').textContent = '编辑宿舍';
        document.getElementById('submitBtn').textContent = '更新';

        const form = document.getElementById('dormitoryForm');
        form.querySelector('input[name="name"]').value = dormitory.name;
        form.querySelector('input[name="total_beds"]').value = dormitory.total_beds;
        form.querySelector('select[name="department_id"]').value = dormitory.department_id || '';
        form.querySelector('textarea[name="description"]').value = dormitory.description || '';

        this.renderDepartmentOptions();
        document.getElementById('dormitoryDialog').style.display = 'flex';
      }

      closeDormitoryDialog() {
        document.getElementById('dormitoryDialog').style.display = 'none';
        document.getElementById('dormitoryForm').reset();
        this.currentDormitory = null;
        this.isEdit = false;
      }

      async handleSubmit() {
        const form = document.getElementById('dormitoryForm');
        const formData = new FormData(form);

        const validator = new FormValidator(form);
        validator.addRule('name', [{ required: true, message: '请输入宿舍名称' }]);
        validator.addRule('total_beds', [{ required: true, message: '请输入总床位数' }]);

        const validation = validator.validate();
        if (!validation.valid) return;

        const submitBtn = document.getElementById('submitBtn');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = '提交中...';
        submitBtn.disabled = true;

        try {
          const data = {
            name: formData.get('name'),
            total_beds: parseInt(formData.get('total_beds')),
            department_id: formData.get('department_id') || null,
            description: formData.get('description') || null
          };

          if (this.isEdit) {
            await api.put(`/v1/dormitories/${this.currentDormitory.id}`, data);
            showMessage('宿舍更新成功', 'success');
          } else {
            await api.post('/v1/dormitories/create', data);
            showMessage('宿舍创建成功', 'success');
          }

          this.closeDormitoryDialog();
          await this.loadDormitories();
        } catch (error) {
          console.error('提交失败:', error);
          showMessage(error.message || '提交失败', 'error');
        } finally {
          submitBtn.textContent = originalText;
          submitBtn.disabled = false;
        }
      }

      async handleDelete(dormitoryId) {
        const dormitory = this.dormitories.find(d => d.id === dormitoryId);
        if (!dormitory) {
          showMessage('宿舍不存在', 'error');
          return;
        }

        const confirmed = await showConfirm(`确定要删除宿舍"${dormitory.name}"吗？`, '删除确认');
        if (!confirmed) return;

        try {
          await api.delete(`/v1/dormitories/${dormitoryId}`);
          showMessage('宿舍删除成功', 'success');
          await this.loadDormitories();
        } catch (error) {
          console.error('删除失败:', error);
          showMessage(error.message || '删除失败', 'error');
        }
      }

      handleSearch() {
        const form = document.getElementById('searchForm');
        const formData = new FormData(form);
        this.searchForm = { name: formData.get('name') || '' };
        this.loadDormitories();
      }

      resetSearch() {
        document.getElementById('searchForm').reset();
        this.searchForm = { name: '' };
        this.loadDormitories();
      }

      async refreshData() {
        await this.loadDormitories();
        showMessage('数据已刷新', 'success');
      }
    }

    // 全局函数
    function showCreateDialog() { window.dormitoriesManager?.showCreateDialog(); }
    function showEditDialog(id) { window.dormitoriesManager?.showEditDialog(id); }
    function closeDormitoryDialog() { window.dormitoriesManager?.closeDormitoryDialog(); }
    function handleSubmit() { window.dormitoriesManager?.handleSubmit(); }
    function handleDelete(id) { window.dormitoriesManager?.handleDelete(id); }
    function handleSearch() { window.dormitoriesManager?.handleSearch(); }
    function resetSearch() { window.dormitoriesManager?.resetSearch(); }
    function refreshData() { window.dormitoriesManager?.refreshData(); }

    // 初始化
    document.addEventListener('DOMContentLoaded', () => {
      window.dormitoriesManager = new DormitoriesManager();
    });
  </script>
</body>
</html>
