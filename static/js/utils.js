// 工具函数库

// API请求工具
class ApiClient {
  constructor(baseURL = '/dorm-manage/api') {
    this.baseURL = baseURL;
    this.loading = null;
  }

  // 显示加载动画
  showLoading(text = '加载中...') {
    this.hideLoading();
    const loadingEl = document.createElement('div');
    loadingEl.className = 'loading-overlay';
    loadingEl.innerHTML = `
      <div class="loading-content">
        <div class="loading"></div>
        <div class="loading-text">${text}</div>
      </div>
    `;
    document.body.appendChild(loadingEl);
    this.loading = loadingEl;
  }

  // 隐藏加载动画
  hideLoading() {
    if (this.loading) {
      document.body.removeChild(this.loading);
      this.loading = null;
    }
  }

  // 获取token
  getToken() {
    return localStorage.getItem('token');
  }

  // 设置token
  setToken(token) {
    localStorage.setItem('token', token);
  }

  // 清除token
  clearToken() {
    localStorage.removeItem('token');
    localStorage.removeItem('userInfo');
  }

  // 通用请求方法
  async request(url, options = {}) {
    const config = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    };

    // 添加认证头
    const token = this.getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // 显示加载动画
    if (options.loading !== false) {
      this.showLoading();
    }

    try {
      const response = await fetch(`${this.baseURL}${url}`, config);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP ${response.status}`);
      }

      // 处理204 No Content响应
      if (response.status === 204) {
        return null;
      }

      // 检查响应是否有内容
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        const data = await response.json();
        return data;
      } else {
        // 如果不是JSON响应，返回null
        return null;
      }
    } catch (error) {
      this.handleError(error);
      throw error;
    } finally {
      this.hideLoading();
    }
  }

  // 错误处理
  handleError(error) {
    let message = '请求失败';
    
    if (error.message.includes('401')) {
      message = '登录已过期，请重新登录';
      this.clearToken();
      setTimeout(() => {
        window.location.href = 'login.html';
      }, 1000);
    } else if (error.message.includes('403')) {
      message = '拒绝访问';
    } else if (error.message.includes('404')) {
      message = '请求的资源不存在';
    } else if (error.message.includes('500')) {
      message = '服务器内部错误';
    } else if (error.message === 'Failed to fetch') {
      message = '网络连接失败';
    } else {
      message = error.message;
    }

    showMessage(message, 'error');
  }

  // GET请求
  get(url, options = {}) {
    return this.request(url, { ...options, method: 'GET' });
  }

  // POST请求
  post(url, data, options = {}) {
    return this.request(url, {
      ...options,
      method: 'POST',
      body: JSON.stringify(data)
    });
  }

  // PUT请求
  put(url, data, options = {}) {
    return this.request(url, {
      ...options,
      method: 'PUT',
      body: JSON.stringify(data)
    });
  }

  // DELETE请求
  delete(url, options = {}) {
    return this.request(url, { ...options, method: 'DELETE' });
  }
}

// 创建全局API客户端实例
const api = new ApiClient();

// 消息提示函数
function showMessage(message, type = 'info', duration = 3000) {
  const messageEl = document.createElement('div');
  messageEl.className = `message message-${type}`;
  messageEl.textContent = message;
  
  document.body.appendChild(messageEl);
  
  // 自动移除
  setTimeout(() => {
    if (messageEl.parentNode) {
      document.body.removeChild(messageEl);
    }
  }, duration);
}

// 确认对话框
function showConfirm(message, title = '提示') {
  return new Promise((resolve) => {
    const overlay = document.createElement('div');
    overlay.className = 'dialog-overlay';
    overlay.innerHTML = `
      <div class="dialog">
        <div class="dialog-header">
          <h3>${title}</h3>
        </div>
        <div class="dialog-body">
          <p>${message}</p>
        </div>
        <div class="dialog-footer">
          <button class="btn" onclick="handleCancel()">取消</button>
          <button class="btn btn-primary" onclick="handleConfirm()">确定</button>
        </div>
      </div>
    `;
    
    document.body.appendChild(overlay);
    
    window.handleConfirm = () => {
      document.body.removeChild(overlay);
      delete window.handleConfirm;
      delete window.handleCancel;
      resolve(true);
    };
    
    window.handleCancel = () => {
      document.body.removeChild(overlay);
      delete window.handleConfirm;
      delete window.handleCancel;
      resolve(false);
    };
  });
}

// 表单验证工具
class FormValidator {
  constructor(form) {
    this.form = form;
    this.rules = {};
  }

  // 添加验证规则
  addRule(field, rules) {
    this.rules[field] = rules;
  }

  // 验证单个字段
  validateField(field, value) {
    const rules = this.rules[field];
    if (!rules) return { valid: true };

    for (const rule of rules) {
      if (rule.required && (!value || value.trim() === '')) {
        return { valid: false, message: rule.message || '此字段为必填项' };
      }
      
      if (rule.min && value.length < rule.min) {
        return { valid: false, message: rule.message || `最少需要${rule.min}个字符` };
      }
      
      if (rule.max && value.length > rule.max) {
        return { valid: false, message: rule.message || `最多允许${rule.max}个字符` };
      }
      
      if (rule.pattern && !rule.pattern.test(value)) {
        return { valid: false, message: rule.message || '格式不正确' };
      }
      
      if (rule.validator && !rule.validator(value)) {
        return { valid: false, message: rule.message || '验证失败' };
      }
    }

    return { valid: true };
  }

  // 验证整个表单
  validate() {
    let isValid = true;
    const errors = {};

    // 清除之前的错误信息
    this.clearErrors();

    // 验证所有字段
    for (const field in this.rules) {
      const input = this.form.querySelector(`[name="${field}"]`);
      if (input) {
        const result = this.validateField(field, input.value);
        if (!result.valid) {
          isValid = false;
          errors[field] = result.message;
          this.showFieldError(input, result.message);
        }
      }
    }

    return { valid: isValid, errors };
  }

  // 显示字段错误
  showFieldError(input, message) {
    const formItem = input.closest('.form-item');
    if (formItem) {
      let errorEl = formItem.querySelector('.form-item__error');
      if (!errorEl) {
        errorEl = document.createElement('div');
        errorEl.className = 'form-item__error';
        formItem.appendChild(errorEl);
      }
      errorEl.textContent = message;
      input.style.borderColor = 'var(--danger-color)';
    }
  }

  // 清除错误信息
  clearErrors() {
    const errorEls = this.form.querySelectorAll('.form-item__error');
    errorEls.forEach(el => el.remove());
    
    const inputs = this.form.querySelectorAll('input, textarea, select');
    inputs.forEach(input => {
      input.style.borderColor = '';
    });
  }
}

// 日期格式化工具
function formatDate(date, format = 'YYYY-MM-DD') {
  if (!date) return '';
  
  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
}

// 防抖函数
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// 节流函数
function throttle(func, limit) {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// 深拷贝
function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime());
  if (obj instanceof Array) return obj.map(item => deepClone(item));
  if (typeof obj === 'object') {
    const clonedObj = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
}

// 导出到全局
window.api = api;
window.showMessage = showMessage;
window.showConfirm = showConfirm;
window.FormValidator = FormValidator;
window.formatDate = formatDate;
window.debounce = debounce;
window.throttle = throttle;
window.deepClone = deepClone;
