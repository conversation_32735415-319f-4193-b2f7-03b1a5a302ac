// 入住记录页面逻辑

class RecordsManager {
  constructor() {
    this.records = [];
    this.dormitories = [];
    this.departments = [];
    this.residents = [];
    this.loading = false;
    this.currentRecord = null;
    this.isEdit = false;
    this.searchForm = {
      dormitory_id: '',
      department_id: '',
      status: '',
      start_date: '',
      end_date: ''
    };
    // 分页相关
    this.pagination = {
      page: 1,
      pageSize: 20,
      total: 0,
      totalPages: 0
    };
    this.init();
  }

  async init() {
    await this.loadInitialData();
    this.setupEventListeners();
    await this.loadRecords();
  }

  // 加载初始数据
  async loadInitialData() {
    try {
      const [dormitoriesRes, departmentsRes, residentsRes] = await Promise.all([
        api.get('/v1/dormitories/list'),
        api.get('/v1/departments/list'),
        api.get('/v1/residents/list')
      ]);

      this.dormitories = dormitoriesRes.items || dormitoriesRes;
      this.departments = departmentsRes.items || departmentsRes;
      this.residents = residentsRes.items || residentsRes;

      this.populateSearchOptions();
      this.populateFormOptions();
    } catch (error) {
      console.error('加载初始数据失败:', error);
      showMessage('加载初始数据失败', 'error');
    }
  }

  // 填充搜索选项
  populateSearchOptions() {
    const dormitorySelect = document.querySelector('#searchForm select[name="dormitory_id"]');
    const departmentSelect = document.querySelector('#searchForm select[name="department_id"]');

    // 填充宿舍选项
    dormitorySelect.innerHTML = '<option value="">请选择宿舍</option>';
    this.dormitories.forEach(dorm => {
      dormitorySelect.innerHTML += `<option value="${dorm.id}">${dorm.name}</option>`;
    });

    // 填充部门选项
    departmentSelect.innerHTML = '<option value="">请选择部门</option>';
    this.departments.forEach(dept => {
      departmentSelect.innerHTML += `<option value="${dept.id}">${dept.name}</option>`;
    });
  }

  // 填充表单选项
  populateFormOptions() {
    const residentSelect = document.querySelector('#recordForm select[name="resident_id"]');
    const dormitorySelect = document.querySelector('#recordForm select[name="dormitory_id"]');

    // 填充住户选项
    residentSelect.innerHTML = '<option value="">请选择住户</option>';
    this.residents.forEach(resident => {
      const label = `${resident.name} (${resident.employee_id || '无员工号'})`;
      residentSelect.innerHTML += `<option value="${resident.id}">${label}</option>`;
    });

    // 填充宿舍选项
    dormitorySelect.innerHTML = '<option value="">请选择宿舍</option>';
    this.dormitories.forEach(dorm => {
      const availableBeds = dorm.available_beds || dorm.total_beds;
      const label = `${dorm.name} (${availableBeds}/${dorm.total_beds})`;
      dormitorySelect.innerHTML += `<option value="${dorm.id}">${label}</option>`;
    });
  }

  // 设置事件监听器
  setupEventListeners() {
    // 住户选择变化
    const residentSelect = document.querySelector('#recordForm select[name="resident_id"]');
    residentSelect.addEventListener('change', (e) => this.onResidentChange(e.target.value));

    // 宿舍选择变化
    const dormitorySelect = document.querySelector('#recordForm select[name="dormitory_id"]');
    dormitorySelect.addEventListener('change', (e) => this.onDormitoryChange(e.target.value));
  }

  // 住户选择变化处理
  onResidentChange(residentId) {
    if (!residentId) return;

    const resident = this.residents.find(r => r.id === residentId);
    if (resident) {
      // 可以在这里自动填充一些信息，比如项目组
      const projectGroupInput = document.querySelector('#recordForm input[name="project_group"]');
      if (resident.project_group && projectGroupInput) {
        projectGroupInput.value = resident.project_group;
      }
    }
  }

  // 宿舍选择变化处理
  onDormitoryChange(dormitoryId) {
    if (!dormitoryId) return;

    const dormitory = this.dormitories.find(d => d.id === dormitoryId);
    if (dormitory && dormitory.available_beds === 0) {
      showMessage('该宿舍已满，请选择其他宿舍', 'warning');
    }
  }

  // 加载记录数据
  async loadRecords() {
    this.loading = true;
    this.renderLoading();

    try {
      const params = new URLSearchParams();

      // 添加分页参数
      const skip = (this.pagination.page - 1) * this.pagination.pageSize;
      params.append('skip', skip.toString());
      params.append('limit', this.pagination.pageSize.toString());

      // 添加搜索参数
      Object.entries(this.searchForm).forEach(([key, value]) => {
        if (value) {
          // 转换参数名以匹配后端API
          if (key === 'start_date') {
            params.append('check_in_start', value);
          } else if (key === 'end_date') {
            params.append('check_in_end', value);
          } else {
            params.append(key, value);
          }
        }
      });

      const response = await api.get(`/v1/records/list?${params.toString()}`);

      // 处理分页响应
      if (response.items) {
        this.records = response.items;
        this.pagination.total = response.total;
        this.pagination.totalPages = response.total_pages;
      } else {
        // 兼容旧格式
        this.records = response;
      }

      this.renderRecords();
      this.renderPagination();
    } catch (error) {
      console.error('加载记录失败:', error);
      this.renderError();
    } finally {
      this.loading = false;
    }
  }

  // 渲染加载状态
  renderLoading() {
    const tbody = document.getElementById('recordsTableBody');
    tbody.innerHTML = `
      <tr>
        <td colspan="10" style="text-align: center; padding: 40px;">
          <div class="loading"></div>
          <div style="margin-top: 12px;">加载中...</div>
        </td>
      </tr>
    `;
  }

  // 渲染记录列表
  renderRecords() {
    const tbody = document.getElementById('recordsTableBody');
    
    if (!this.records || this.records.length === 0) {
      tbody.innerHTML = `
        <tr>
          <td colspan="10" style="text-align: center; padding: 40px;">
            <div class="empty">
              <div class="empty-description">暂无记录数据</div>
            </div>
          </td>
        </tr>
      `;
      return;
    }

    tbody.innerHTML = this.records.map(record => `
      <tr>
        <td>${record.resident_name}</td>
        <td>${record.department_name}</td>
        <td>${record.project_group || '-'}</td>
        <td>${record.dormitory_name}</td>
        <td>${record.check_in_date}</td>
        <td>${record.check_out_date || '-'}</td>
        <td>${record.days_stayed}</td>
        <td>
          <span class="tag tag-${this.getStatusTagType(record.status)}">
            ${this.getStatusText(record.status)}
          </span>
        </td>
        <td>${record.notes || '-'}</td>
        <td>
          <div class="action-buttons">
            ${record.status === 'ACTIVE' ? 
              `<button class="btn btn-warning" onclick="showCheckoutDialog('${record.id}')">办理离开</button>` : 
              ''
            }
            <button class="btn btn-primary" onclick="showEditDialog('${record.id}')">编辑</button>
            <button class="btn btn-danger" onclick="handleDelete('${record.id}')">删除</button>
          </div>
        </td>
      </tr>
    `).join('');
  }

  // 渲染错误状态
  renderError() {
    const tbody = document.getElementById('recordsTableBody');
    tbody.innerHTML = `
      <tr>
        <td colspan="10" style="text-align: center; padding: 40px;">
          <div class="empty">
            <div class="empty-description">加载失败，请重试</div>
          </div>
        </td>
      </tr>
    `;
  }

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      'ACTIVE': '入住中',
      'COMPLETED': '已离开',
      'CANCELLED': '已取消'
    };
    return statusMap[status] || status;
  }

  // 获取状态标签类型
  getStatusTagType(status) {
    const typeMap = {
      'ACTIVE': 'success',
      'COMPLETED': 'info',
      'CANCELLED': 'warning'
    };
    return typeMap[status] || 'info';
  }

  // 搜索处理
  handleSearch() {
    const form = document.getElementById('searchForm');
    const formData = new FormData(form);

    this.searchForm = {
      dormitory_id: formData.get('dormitory_id') || '',
      department_id: formData.get('department_id') || '',
      status: formData.get('status') || '',
      start_date: formData.get('start_date') || '',
      end_date: formData.get('end_date') || ''
    };

    // 搜索时重置到第一页
    this.pagination.page = 1;
    this.loadRecords();
  }

  // 重置搜索
  resetSearch() {
    document.getElementById('searchForm').reset();
    this.searchForm = {
      dormitory_id: '',
      department_id: '',
      status: '',
      start_date: '',
      end_date: ''
    };
    // 重置时回到第一页
    this.pagination.page = 1;
    this.loadRecords();
  }

  // 分页相关方法
  goToPage(page) {
    if (page >= 1 && page <= this.pagination.totalPages) {
      this.pagination.page = page;
      this.loadRecords();
    }
  }

  goToPrevPage() {
    if (this.pagination.page > 1) {
      this.pagination.page--;
      this.loadRecords();
    }
  }

  goToNextPage() {
    if (this.pagination.page < this.pagination.totalPages) {
      this.pagination.page++;
      this.loadRecords();
    }
  }

  goToLastPage() {
    this.pagination.page = this.pagination.totalPages;
    this.loadRecords();
  }

  changePageSize(pageSize) {
    this.pagination.pageSize = parseInt(pageSize);
    this.pagination.page = 1; // 重置到第一页
    this.loadRecords();
  }

  // 渲染分页控件
  renderPagination() {
    const container = document.getElementById('paginationContainer');
    const info = document.getElementById('paginationInfo');
    const pages = document.getElementById('paginationPages');
    const firstBtn = document.getElementById('firstPageBtn');
    const prevBtn = document.getElementById('prevPageBtn');
    const nextBtn = document.getElementById('nextPageBtn');
    const lastBtn = document.getElementById('lastPageBtn');

    if (this.pagination.total === 0) {
      container.style.display = 'none';
      return;
    }

    container.style.display = 'flex';

    // 更新信息
    const start = (this.pagination.page - 1) * this.pagination.pageSize + 1;
    const end = Math.min(this.pagination.page * this.pagination.pageSize, this.pagination.total);
    info.textContent = `显示第 ${start}-${end} 条，共 ${this.pagination.total} 条记录`;

    // 更新按钮状态
    firstBtn.disabled = this.pagination.page === 1;
    prevBtn.disabled = this.pagination.page === 1;
    nextBtn.disabled = this.pagination.page === this.pagination.totalPages;
    lastBtn.disabled = this.pagination.page === this.pagination.totalPages;

    // 生成页码按钮
    this.renderPageNumbers(pages);
  }

  renderPageNumbers(container) {
    container.innerHTML = '';

    const currentPage = this.pagination.page;
    const totalPages = this.pagination.totalPages;

    // 计算显示的页码范围
    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(totalPages, currentPage + 2);

    // 确保显示5个页码（如果可能）
    if (endPage - startPage < 4) {
      if (startPage === 1) {
        endPage = Math.min(totalPages, startPage + 4);
      } else if (endPage === totalPages) {
        startPage = Math.max(1, endPage - 4);
      }
    }

    // 添加第一页和省略号
    if (startPage > 1) {
      this.createPageButton(container, 1);
      if (startPage > 2) {
        this.createEllipsis(container);
      }
    }

    // 添加页码按钮
    for (let i = startPage; i <= endPage; i++) {
      this.createPageButton(container, i, i === currentPage);
    }

    // 添加省略号和最后一页
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        this.createEllipsis(container);
      }
      this.createPageButton(container, totalPages);
    }
  }

  createPageButton(container, page, isActive = false) {
    const button = document.createElement('button');
    button.className = `pagination-page ${isActive ? 'active' : ''}`;
    button.textContent = page;
    button.onclick = () => this.goToPage(page);
    container.appendChild(button);
  }

  createEllipsis(container) {
    const ellipsis = document.createElement('span');
    ellipsis.className = 'pagination-ellipsis';
    ellipsis.textContent = '...';
    container.appendChild(ellipsis);
  }

  // 显示活跃记录
  showActiveRecords() {
    this.searchForm.status = 'ACTIVE';
    document.querySelector('#searchForm select[name="status"]').value = 'ACTIVE';
    this.loadRecords();
  }

  // 刷新数据
  async refreshData() {
    await this.loadInitialData();
    await this.loadRecords();
    showMessage('数据已刷新', 'success');
  }

  // 显示创建对话框
  showCreateDialog() {
    this.isEdit = false;
    this.currentRecord = null;
    document.getElementById('dialogTitle').textContent = '新增入住记录';
    document.getElementById('submitBtn').textContent = '创建';
    document.getElementById('recordForm').reset();
    document.getElementById('recordDialog').style.display = 'flex';
  }

  // 显示编辑对话框
  showEditDialog(recordId) {
    const record = this.records.find(r => r.id === recordId);
    if (!record) {
      showMessage('记录不存在', 'error');
      return;
    }

    this.isEdit = true;
    this.currentRecord = record;
    document.getElementById('dialogTitle').textContent = '编辑入住记录';
    document.getElementById('submitBtn').textContent = '更新';

    // 填充表单数据
    const form = document.getElementById('recordForm');
    form.querySelector('select[name="resident_id"]').value = record.resident_id;
    form.querySelector('select[name="dormitory_id"]').value = record.dormitory_id;
    form.querySelector('input[name="project_group"]').value = record.project_group || '';
    form.querySelector('input[name="check_in_date"]').value = record.check_in_date;
    form.querySelector('textarea[name="notes"]').value = record.notes || '';

    document.getElementById('recordDialog').style.display = 'flex';
  }

  // 关闭记录对话框
  closeRecordDialog() {
    document.getElementById('recordDialog').style.display = 'none';
    document.getElementById('recordForm').reset();
    this.currentRecord = null;
    this.isEdit = false;
  }

  // 提交表单
  async handleSubmit() {
    const form = document.getElementById('recordForm');
    const formData = new FormData(form);

    // 表单验证
    const validator = new FormValidator(form);
    validator.addRule('resident_id', [{ required: true, message: '请选择住户' }]);
    validator.addRule('dormitory_id', [{ required: true, message: '请选择宿舍' }]);
    validator.addRule('project_group', [{ required: true, message: '请输入项目组' }]);
    validator.addRule('check_in_date', [{ required: true, message: '请选择入住日期' }]);

    const validation = validator.validate();
    if (!validation.valid) {
      return;
    }

    const submitBtn = document.getElementById('submitBtn');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = '提交中...';
    submitBtn.disabled = true;

    try {
      const data = {
        resident_id: formData.get('resident_id'),
        dormitory_id: formData.get('dormitory_id'),
        project_group: formData.get('project_group'),
        check_in_date: formData.get('check_in_date'),
        notes: formData.get('notes') || null
      };

      if (this.isEdit) {
        await api.put(`/v1/records/${this.currentRecord.id}`, data);
        showMessage('记录更新成功', 'success');
      } else {
        await api.post('/v1/records/create', data);
        showMessage('记录创建成功', 'success');
      }

      this.closeRecordDialog();
      await this.loadRecords();
    } catch (error) {
      console.error('提交失败:', error);
      showMessage(error.message || '提交失败', 'error');
    } finally {
      submitBtn.textContent = originalText;
      submitBtn.disabled = false;
    }
  }

  // 显示离开对话框
  showCheckoutDialog(recordId) {
    const record = this.records.find(r => r.id === recordId);
    if (!record) {
      showMessage('记录不存在', 'error');
      return;
    }

    this.currentRecord = record;

    // 填充住户信息
    const checkoutInfo = document.getElementById('checkoutInfo');
    checkoutInfo.innerHTML = `
      <div class="descriptions-item">
        <div class="descriptions-label">住户姓名</div>
        <div class="descriptions-content">${record.resident_name}</div>
      </div>
      <div class="descriptions-item">
        <div class="descriptions-label">宿舍</div>
        <div class="descriptions-content">${record.dormitory_name}</div>
      </div>
      <div class="descriptions-item">
        <div class="descriptions-label">床位号</div>
        <div class="descriptions-content">${record.bed_number || '-'}号床</div>
      </div>
      <div class="descriptions-item">
        <div class="descriptions-label">入住日期</div>
        <div class="descriptions-content">${record.check_in_date}</div>
      </div>
      <div class="descriptions-item">
        <div class="descriptions-label">已住天数</div>
        <div class="descriptions-content">${record.days_stayed}天</div>
      </div>
    `;

    // 设置默认离开日期为今天
    const today = new Date().toISOString().split('T')[0];
    document.querySelector('#checkoutForm input[name="checkout_date"]').value = today;
    document.querySelector('#checkoutForm input[name="checkout_date"]').min = record.check_in_date;

    document.getElementById('checkoutDialog').style.display = 'flex';
  }

  // 关闭离开对话框
  closeCheckoutDialog() {
    document.getElementById('checkoutDialog').style.display = 'none';
    document.getElementById('checkoutForm').reset();
    this.currentRecord = null;
  }

  // 处理离开
  async handleCheckout() {
    const form = document.getElementById('checkoutForm');
    const formData = new FormData(form);

    // 表单验证
    const validator = new FormValidator(form);
    validator.addRule('checkout_date', [{ required: true, message: '请选择离开日期' }]);

    const validation = validator.validate();
    if (!validation.valid) {
      return;
    }

    const checkoutBtn = document.getElementById('checkoutBtn');
    const originalText = checkoutBtn.textContent;
    checkoutBtn.textContent = '处理中...';
    checkoutBtn.disabled = true;

    try {
      const checkoutDate = formData.get('checkout_date');
      const notes = formData.get('notes') || null;

      // 构建查询参数
      const params = new URLSearchParams();
      params.append('checkout_date', checkoutDate);
      if (notes) {
        params.append('notes', notes);
      }

      await api.post(`/v1/records/${this.currentRecord.id}/checkout?${params.toString()}`);
      showMessage('离开办理成功', 'success');

      this.closeCheckoutDialog();
      await this.loadRecords();
    } catch (error) {
      console.error('办理离开失败:', error);
      showMessage(error.message || '办理离开失败', 'error');
    } finally {
      checkoutBtn.textContent = originalText;
      checkoutBtn.disabled = false;
    }
  }

  // 删除记录
  async handleDelete(recordId) {
    const record = this.records.find(r => r.id === recordId);
    if (!record) {
      showMessage('记录不存在', 'error');
      return;
    }

    const confirmed = await showConfirm(
      `确定要删除住户"${record.resident_name}"的入住记录吗？`,
      '删除确认'
    );

    if (!confirmed) return;

    try {
      await api.delete(`/v1/records/${recordId}`);
      showMessage('记录删除成功', 'success');
      await this.loadRecords();
    } catch (error) {
      console.error('删除失败:', error);
      showMessage(error.message || '删除失败', 'error');
    }
  }
}

// 全局函数
function showCreateDialog() {
  if (window.recordsManager) {
    window.recordsManager.showCreateDialog();
  }
}

function showEditDialog(recordId) {
  if (window.recordsManager) {
    window.recordsManager.showEditDialog(recordId);
  }
}

function closeRecordDialog() {
  if (window.recordsManager) {
    window.recordsManager.closeRecordDialog();
  }
}

function handleSubmit() {
  if (window.recordsManager) {
    window.recordsManager.handleSubmit();
  }
}

function showCheckoutDialog(recordId) {
  if (window.recordsManager) {
    window.recordsManager.showCheckoutDialog(recordId);
  }
}

function closeCheckoutDialog() {
  if (window.recordsManager) {
    window.recordsManager.closeCheckoutDialog();
  }
}

function handleCheckout() {
  if (window.recordsManager) {
    window.recordsManager.handleCheckout();
  }
}

function handleDelete(recordId) {
  if (window.recordsManager) {
    window.recordsManager.handleDelete(recordId);
  }
}

function handleSearch() {
  if (window.recordsManager) {
    window.recordsManager.handleSearch();
  }
}

function resetSearch() {
  if (window.recordsManager) {
    window.recordsManager.resetSearch();
  }
}

function showActiveRecords() {
  if (window.recordsManager) {
    window.recordsManager.showActiveRecords();
  }
}

function refreshData() {
  if (window.recordsManager) {
    window.recordsManager.refreshData();
  }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
  window.recordsManager = new RecordsManager();
});
