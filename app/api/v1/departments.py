"""
部门管理API
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.api.deps import get_db
from app.schemas.department import DepartmentCreate, DepartmentUpdate, DepartmentResponse
from app.services.department_service import DepartmentService
from app.auth.dependencies import get_current_user

router = APIRouter()


@router.get("/list", response_model=List[DepartmentResponse], summary="获取部门列表")
async def get_departments(
    skip: int = 0,
    limit: int = 100,
    is_active: bool = None,
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    """
    获取部门列表

    - **skip**: 跳过记录数
    - **limit**: 限制记录数
    - **is_active**: 是否启用筛选
    """
    service = DepartmentService(db)
    return await service.get_departments(skip=skip, limit=limit, is_active=is_active)


@router.post("/create", response_model=DepartmentResponse, status_code=status.HTTP_201_CREATED, summary="创建部门")
async def create_department(
    department: DepartmentCreate,
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    """创建新部门"""
    service = DepartmentService(db)
    return await service.create_department(department)


@router.get("/{department_id}", response_model=DepartmentResponse, summary="获取部门详情")
async def get_department(
    department_id: str,
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    """根据ID获取部门详情"""
    service = DepartmentService(db)
    department = await service.get_department_by_id(department_id)
    if not department:
        raise HTTPException(status_code=404, detail="部门不存在")
    return department


@router.put("/{department_id}", response_model=DepartmentResponse, summary="更新部门")
async def update_department(
    department_id: str,
    department: DepartmentUpdate,
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    """更新部门信息"""
    service = DepartmentService(db)
    updated_department = await service.update_department(department_id, department)
    if not updated_department:
        raise HTTPException(status_code=404, detail="部门不存在")
    return updated_department


@router.delete("/{department_id}", status_code=status.HTTP_204_NO_CONTENT, summary="删除部门")
async def delete_department(
    department_id: str,
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    """删除部门"""
    service = DepartmentService(db)
    success = await service.delete_department(department_id)
    if not success:
        raise HTTPException(status_code=404, detail="部门不存在")
