"""
住户管理API
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from app.api.deps import get_db
from app.services.resident_service import ResidentService
from app.schemas.resident import ResidentCreate, ResidentUpdate, ResidentResponse
from app.core.logging import logger
from app.auth.dependencies import get_current_user

router = APIRouter()


@router.get("/list", response_model=List[ResidentResponse])
def get_residents(
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="限制记录数"),
    department_id: Optional[str] = Query(None, description="部门ID筛选"),
    is_active: Optional[bool] = Query(None, description="是否启用筛选"),
    keyword: Optional[str] = Query(None, description="搜索关键词"),
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    """
    获取住户列表

    支持以下筛选条件：
    - department_id: 按部门筛选
    - is_active: 按启用状态筛选
    - keyword: 按姓名、员工号、电话、邮箱搜索
    """
    try:
        service = ResidentService(db)
        return service.get_residents(
            skip=skip,
            limit=limit,
            department_id=department_id,
            is_active=is_active,
            keyword=keyword
        )
    except Exception as e:
        logger.error(f"获取住户列表API失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/create", response_model=ResidentResponse)
def create_resident(
    resident_data: ResidentCreate,
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    """
    创建住户

    创建新的住户记录，需要提供：
    - 姓名（必填）
    - 部门ID（必填）
    - 员工号（可选，但不能重复）
    - 联系方式（可选）
    """
    try:
        service = ResidentService(db)
        return service.create_resident(resident_data)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"创建住户API失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{resident_id}", response_model=ResidentResponse)
def get_resident(
    resident_id: str,
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    """
    获取住户详情
    
    根据住户ID获取详细信息，包括：
    - 基本信息
    - 所属部门
    - 当前住宿情况
    """
    try:
        service = ResidentService(db)
        resident = service.get_resident(resident_id)
        if not resident:
            raise HTTPException(status_code=404, detail="住户不存在")
        return resident
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取住户详情API失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{resident_id}", response_model=ResidentResponse)
def update_resident(
    resident_id: str,
    resident_data: ResidentUpdate,
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    """
    更新住户信息
    
    可以更新住户的以下信息：
    - 姓名
    - 员工号
    - 联系方式
    - 所属部门
    - 启用状态
    """
    try:
        service = ResidentService(db)
        resident = service.update_resident(resident_id, resident_data)
        if not resident:
            raise HTTPException(status_code=404, detail="住户不存在")
        return resident
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新住户API失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{resident_id}")
def delete_resident(
    resident_id: str,
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    """
    删除住户
    
    删除住户记录，注意：
    - 只能删除没有活跃入住记录的住户
    - 删除操作不可恢复
    """
    try:
        service = ResidentService(db)
        success = service.delete_resident(resident_id)
        if not success:
            raise HTTPException(status_code=404, detail="住户不存在")
        return {"message": "住户删除成功"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除住户API失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/department/{department_id}", response_model=List[ResidentResponse])
def get_residents_by_department(
    department_id: str,
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    """
    根据部门获取住户列表
    
    获取指定部门下的所有启用住户
    """
    try:
        service = ResidentService(db)
        return service.get_residents_by_department(department_id)
    except Exception as e:
        logger.error(f"根据部门获取住户列表API失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{resident_id}/current-residence")
def get_resident_current_residence(
    resident_id: str,
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    """
    获取住户当前住宿情况
    
    返回住户当前的入住记录信息
    """
    try:
        service = ResidentService(db)
        resident = service.get_resident(resident_id)
        if not resident:
            raise HTTPException(status_code=404, detail="住户不存在")
        
        return {
            "resident_id": resident_id,
            "resident_name": resident.name,
            "current_dormitory": resident.current_dormitory,
            "current_bed_number": resident.current_bed_number,
            "has_active_residence": resident.current_dormitory is not None
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取住户当前住宿情况API失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
