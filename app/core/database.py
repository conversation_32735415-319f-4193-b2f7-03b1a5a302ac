"""
数据库配置模块
"""
import time
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import OperationalError
from typing import Generator

from .config import settings
from .logging import get_logger

logger = get_logger(__name__)

# 创建数据库引擎
engine = create_engine(
    settings.database_url,
    echo=settings.database_echo,
    # SQLite特定配置
    connect_args={"check_same_thread": False} if "sqlite" in settings.database_url else {},
    # MySQL特定配置
    pool_pre_ping=True,  # 连接池预检查
    pool_recycle=3600,   # 连接回收时间（1小时）
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def get_db() -> Generator[Session, None, None]:
    """
    获取数据库会话
    
    Yields:
        Session: 数据库会话
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def wait_for_database(max_retries: int = 30, retry_interval: int = 2):
    """等待数据库连接可用"""
    for attempt in range(max_retries):
        try:
            # 尝试连接数据库
            with engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            logger.info("数据库连接成功")
            return True
        except OperationalError as e:
            if attempt < max_retries - 1:
                logger.warning(f"数据库连接失败，第 {attempt + 1} 次重试: {str(e)}")
                time.sleep(retry_interval)
            else:
                logger.error(f"数据库连接失败，已达到最大重试次数: {str(e)}")
                raise
    return False


def create_tables():
    """创建所有数据表"""
    # 等待数据库连接可用
    wait_for_database()

    # 确保所有模型都被导入
    from app.models import Department, Dormitory, Resident, ResidenceRecord, MonthlyReport, DailyAllocation
    from app.models.base import Base
    Base.metadata.create_all(bind=engine)


def drop_tables():
    """删除所有数据表"""
    from app.models.base import Base
    Base.metadata.drop_all(bind=engine)
